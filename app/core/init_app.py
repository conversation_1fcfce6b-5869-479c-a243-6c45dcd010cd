import shutil
from datetime import datetime

from aerich import Command
from fastapi import <PERSON>AP<PERSON>
from fastapi.middleware import Middleware
from fastapi.middleware.cors import CORSMiddleware
from tortoise.expressions import Q

from app.api import api_router
from app.controllers.api import api_controller
from app.controllers.user import UserCreate, user_controller
from app.core.exceptions import (
    DoesNotExist,
    DoesNotExistHandle,
    HTTPException,
    HttpExcHandle,
    IntegrityError,
    IntegrityHandle,
    RequestValidationError,
    RequestValidationHandle,
    ResponseValidationError,
    ResponseValidationHandle,
)
from app.log import logger
from app.models.admin import Api, Dept, DeptClosure, Menu, Role
from app.models.food_stuff import Unit, Nutrient, FoodStuff, FoodStuffType
from app.schemas.depts import DeptType
from app.schemas.menus import MenuType
from app.settings.config import settings

from .middlewares import BackGroundTaskMiddleware, HttpAuditLogMiddleware


def make_middlewares():
    middleware = [
        Middleware(
            CORSMiddleware,
            allow_origins=settings.CORS_ORIGINS,
            allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
            allow_methods=settings.CORS_ALLOW_METHODS,
            allow_headers=settings.CORS_ALLOW_HEADERS,
        ),
        Middleware(BackGroundTaskMiddleware),
        Middleware(
            HttpAuditLogMiddleware,
            methods=["GET", "POST", "PUT", "DELETE"],
            exclude_paths=[
                "/api/v1/base/access_token",
                "/docs",
                "/openapi.json",
            ],
        ),
    ]
    return middleware


def register_exceptions(app: FastAPI):
    app.add_exception_handler(DoesNotExist, DoesNotExistHandle)
    app.add_exception_handler(HTTPException, HttpExcHandle)
    app.add_exception_handler(IntegrityError, IntegrityHandle)
    app.add_exception_handler(RequestValidationError, RequestValidationHandle)
    app.add_exception_handler(ResponseValidationError, ResponseValidationHandle)


def register_routers(app: FastAPI, prefix: str = "/api"):
    app.include_router(api_router, prefix=prefix)


async def init_superuser():
    user = await user_controller.model.exists()
    if not user:
        await user_controller.create_user(
            UserCreate(
                username="admin",
                email="<EMAIL>",
                password="123456",
                is_active=True,
                is_superuser=True,
                phone="13800000000"
            )
        )


async def init_menus():
    menus = await Menu.exists()
    if not menus:
        parent_menu = await Menu.create(
            menu_type=MenuType.CATALOG,
            name="系统管理",
            path="/system",
            order=1,
            parent_id=0,
            icon="carbon:gui-management",
            is_hidden=False,
            component="Layout",
            keepalive=False,
            redirect="/system/user",
        )
        children_menu = [
            Menu(
                menu_type=MenuType.MENU,
                name="用户管理",
                path="user",
                order=1,
                parent_id=parent_menu.id,
                icon="material-symbols:person-outline-rounded",
                is_hidden=False,
                component="/system/user",
                keepalive=False,
            ),
            Menu(
                menu_type=MenuType.MENU,
                name="角色管理",
                path="role",
                order=2,
                parent_id=parent_menu.id,
                icon="carbon:user-role",
                is_hidden=False,
                component="/system/role",
                keepalive=False,
            ),
            Menu(
                menu_type=MenuType.MENU,
                name="菜单管理",
                path="menu",
                order=3,
                parent_id=parent_menu.id,
                icon="material-symbols:list-alt-outline",
                is_hidden=False,
                component="/system/menu",
                keepalive=False,
            ),
            Menu(
                menu_type=MenuType.MENU,
                name="API管理",
                path="api",
                order=4,
                parent_id=parent_menu.id,
                icon="ant-design:api-outlined",
                is_hidden=False,
                component="/system/api",
                keepalive=False,
            ),
            Menu(
                menu_type=MenuType.MENU,
                name="部门管理",
                path="dept",
                order=5,
                parent_id=parent_menu.id,
                icon="mingcute:department-line",
                is_hidden=False,
                component="/system/dept",
                keepalive=False,
            ),
            Menu(
                menu_type=MenuType.MENU,
                name="审计日志",
                path="auditlog",
                order=6,
                parent_id=parent_menu.id,
                icon="ph:clipboard-text-bold",
                is_hidden=False,
                component="/system/auditlog",
                keepalive=False,
            ),
        ]
        await Menu.bulk_create(children_menu)
        await Menu.create(
            menu_type=MenuType.MENU,
            name="一级菜单",
            path="/top-menu",
            order=2,
            parent_id=0,
            icon="material-symbols:featured-play-list-outline",
            is_hidden=False,
            component="/top-menu",
            keepalive=False,
            redirect="",
        )
        food_stuff_menu = await Menu.create(
            menu_type=MenuType.CATALOG,
            name="食材管理",
            path="/food-stuff",
            order=7,
            parent_id=0,
            icon="material-symbols:food-bank-outline",
            is_hidden=False,
            component="Layout",
            keepalive=False,
        )
        children_menu = [
            Menu(
                menu_type=MenuType.MENU,
                name="食材列表",
                path="food-stuff",
                order=1,
                parent_id=food_stuff_menu.id,
                icon="material-symbols:food-bank-outline",
                is_hidden=False,
                component="/food-stuff/food-stuff",
                keepalive=False,
            ),
            Menu(
                menu_type=MenuType.MENU,
                name="食材库存",
                path="food-stuff-store",
                order=2,
                parent_id=food_stuff_menu.id,
                icon="material-symbols:inventory-2-outline",
                is_hidden=False,
                component="/food-stuff/food-stuff-store",
                keepalive=False,
            ),
            Menu(
                menu_type=MenuType.MENU,
                name="计量单位",
                path="unit",
                order=3,
                parent_id=food_stuff_menu.id,
                icon="formkit:unit",
                is_hidden=False,
                component="/food-stuff/unit",
                keepalive=False,
            ),
            
        ]
        await Menu.bulk_create(children_menu)

        await Menu.create(
            menu_type=MenuType.MENU,
            name="每周食谱",
            path="/food-menu",
            order=8,
            parent_id=0,
            icon="mingcute:menu-line",
            is_hidden=False,
            component="/food-menu",
            keepalive=False,
        )

        await Menu.create(
            menu_type=MenuType.MENU,
            name="供应商管理",
            path="/supplier",
            order=8,
            parent_id=0,
            icon="mingcute:supplier-line",
            is_hidden=False,
            component="/supplier",
            keepalive=False,
        )
        await Menu.create(
            menu_type=MenuType.CATALOG,
            name="订单管理",
            path="/orders",
            order=9,
            parent_id=0,
            icon="mingcute:order-line",
            is_hidden=False,
            component="/orders",
            keepalive=False,
        )
        await Menu.create(
            menu_type=MenuType.MENU,
            name="班级管理",
            path="/class",
            order=1,
            parent_id=9,
            icon="mingcute:order-line",
            is_hidden=False,
            component="/class",
            keepalive=False,
        )

        await Menu.create(
            menu_type=MenuType.MENU,
            name="菜品管理",
            path="/dish",
            order=4,
            parent_id=food_stuff_menu.id,
            icon="material-symbols:restaurant-menu-outline",
            is_hidden=False,
            component="/dish",
            keepalive=False,
        )





async def init_apis():
    apis = await api_controller.model.exists()
    if not apis:
        await api_controller.refresh_api()


async def init_db():
    command = Command(tortoise_config=settings.TORTOISE_ORM)
    try:
        await command.init_db(safe=True)
    except FileExistsError:
        pass

    await command.init()
    try:
        await command.migrate()
    except AttributeError:
        logger.warning("unable to retrieve model history from database, model history will be created from scratch")
        shutil.rmtree("migrations")
        await command.init_db(safe=True)

    await command.upgrade(run_in_transaction=True)


async def init_roles():
    roles = await Role.exists()
    if not roles:
        admin_role = await Role.create(
            name="管理员",
            desc="管理员角色",
        )
        user_role = await Role.create(
            name="普通用户",
            desc="普通用户角色",
        )

        # 分配所有API给管理员角色
        all_apis = await Api.all()
        await admin_role.apis.add(*all_apis)
        # 分配所有菜单给管理员和普通用户
        all_menus = await Menu.all()
        await admin_role.menus.add(*all_menus)
        await user_role.menus.add(*all_menus)

        # 为普通用户分配基本API
        basic_apis = await Api.filter(Q(method__in=["GET"]) | Q(tags="基础模块"))
        await user_role.apis.add(*basic_apis)

async def init_units():
    units = await Unit.exists()
    if not units:
        unit_list = [
            Unit(
                unit_name="克",
            ),
            Unit(
                unit_name="公斤",
            ),
            Unit(
                unit_name="斤",
            ),
            Unit(
                unit_name="两",
            ),
            Unit(
                unit_name="升",
            ),
            Unit(
                unit_name="包",
            ),
            Unit(
                unit_name="袋",
            ),
            Unit(
                unit_name="瓶",
            ),
            Unit(
                unit_name="盒",
            ),
            Unit(
                unit_name="个",
            ),
            Unit(
                unit_name="只",
            ),
            Unit(
                unit_name="条",
            ),
            Unit(
                unit_name="根",
            ),
            Unit(
                unit_name="片",
            ),
            Unit(
                unit_name="粒",
            ),
            Unit(
                unit_name="块",
            ),
            Unit(
                unit_name="吨",
            )
        ]
        await Unit.bulk_create(unit_list)


async def init_demo_departments_roles_users_and_food_stuff():
    departments = await Dept.exists()
    if not departments:
        # 创建学校部门
        school_1 = await Dept.create(
            name="第一中学",
            type=DeptType.SCHOOL,
            phone="13500000001",
            address="北京市海淀区学院路123号",
            email="<EMAIL>",
            desc="示例学校1",
            order=1,
            parent_id=0
        )

        school_2 = await Dept.create(
            name="实验小学",
            type=DeptType.SCHOOL,
            phone="13500000002",
            address="北京市朝阳区建国路456号",
            email="<EMAIL>",
            desc="示例学校2",
            order=2,
            parent_id=0
        )

        # 创建供应商部门
        supplier_1 = await Dept.create(
            name="优质食材供应商",
            type=DeptType.SUPPLY,
            phone="13600000001",
            address="北京市丰台区丰台路789号",
            email="<EMAIL>",
            desc="主要提供新鲜蔬菜水果",
            order=3,
            parent_id=0
        )

        supplier_2 = await Dept.create(
            name="健康餐饮供应链",
            type=DeptType.SUPPLY,
            phone="13600000002",
            address="北京市西城区西长安街101号",
            email="<EMAIL>",
            desc="专注于学生营养餐供应",
            order=4,
            parent_id=0
        )

        # 为学校创建班级子部门
        class_1 = await Dept.create(
            name="高一(1)班",
            type=DeptType.SCHOOL,
            phone="",
            address="",
            email="",
            desc="第一中学高一1班",
            order=1,
            parent_id=school_1.id
        )

        class_2 = await Dept.create(
            name="一年级(2)班",
            type=DeptType.SCHOOL,
            phone="",
            address="",
            email="",
            desc="实验小学一年级2班",
            order=1,
            parent_id=school_2.id
        )

        # 为部门添加关系记录
        # 添加自身关系
        await DeptClosure.bulk_create([
            DeptClosure(ancestor=school_1.id, descendant=school_1.id, level=0),
            DeptClosure(ancestor=school_2.id, descendant=school_2.id, level=0),
            DeptClosure(ancestor=supplier_1.id, descendant=supplier_1.id, level=0),
            DeptClosure(ancestor=supplier_2.id, descendant=supplier_2.id, level=0),
            DeptClosure(ancestor=class_1.id, descendant=class_1.id, level=0),
            DeptClosure(ancestor=class_2.id, descendant=class_2.id, level=0),
        ])

        # 添加父子关系
        await DeptClosure.bulk_create([
            DeptClosure(ancestor=school_1.id, descendant=class_1.id, level=1),
            DeptClosure(ancestor=school_2.id, descendant=class_2.id, level=1),
        ])

        # 创建角色

        # 创建学校角色
        school_role = await Role.create(
            name="学校角色",
            desc="学校管理人员专用角色"
        )

        # 创建供应商角色
        supplier_role = await Role.create(
            name="供应商角色",
            desc="供应商管理人员专用角色"
        )

        # 为角色分配API权限和菜单权限
        # 分配所有菜单
        all_menus = await Menu.all()
        await school_role.menus.add(*all_menus)
        await supplier_role.menus.add(*all_menus)

        # 获取基本API权限
        basic_apis = await Api.filter(Q(method__in=["GET"]) | Q(tags="基础模块") | Q(tags="部门模块") |Q(tags="食材管理") | Q(tags="食材库存管理") | Q(tags="订单模块") | Q(tags="食谱菜单模块") | Q(tags="菜品管理"))

        # 获取其他API权限
        all_apis = await Api.all()

        # 分配API权限
        await school_role.apis.add(*basic_apis)
        await supplier_role.apis.add(*basic_apis)

        # 为每个部门创建用户
        # 学校1用户
        await user_controller.create_user(
            UserCreate(
                username="s1a",
                email="<EMAIL>",
                password="123456",
                is_active=True,
                is_superuser=False,
                phone="13500000001",
                alias="第一中学管理员"
            )
        )
        school1_user = await user_controller.get_by_username("s1a")
        school1_user.dept_id = school_1.id
        await school1_user.save()
        await school1_user.roles.add(school_role)

        # 学校2用户
        await user_controller.create_user(
            UserCreate(
                username="s2a",
                email="<EMAIL>",
                password="123456",
                is_active=True,
                is_superuser=False,
                phone="13500000002",
                alias="实验小学管理员"
            )
        )
        school2_user = await user_controller.get_by_username("s2a")
        school2_user.dept_id = school_2.id
        await school2_user.save()
        await school2_user.roles.add(school_role)

        # 班级1用户
        await user_controller.create_user(
            UserCreate(
                username="c1t",
                email="<EMAIL>",
                password="123456",
                is_active=True,
                is_superuser=False,
                phone="13700000001",
                alias="高一(1)班班主任"
            )
        )
        class1_user = await user_controller.get_by_username("c1t")
        class1_user.dept_id = class_1.id
        await class1_user.save()
        await class1_user.roles.add(school_role)

        # 班级2用户
        await user_controller.create_user(
            UserCreate(
                username="c2t",
                email="<EMAIL>",
                password="123456",
                is_active=True,
                is_superuser=False,
                phone="13700000002",
                alias="一年级(2)班班主任"
            )
        )
        class2_user = await user_controller.get_by_username("c2t")
        class2_user.dept_id = class_2.id
        await class2_user.save()
        await class2_user.roles.add(school_role)

        # 供应商1用户
        await user_controller.create_user(
            UserCreate(
                username="g1a",
                email="<EMAIL>",
                password="123456",
                is_active=True,
                is_superuser=False,
                phone="13600000001",
                alias="供应商1管理员"
            )
        )
        supplier1_user = await user_controller.get_by_username("g1a")
        supplier1_user.dept_id = supplier_1.id
        await supplier1_user.save()
        await supplier1_user.roles.add(supplier_role)

        # 供应商2用户
        await user_controller.create_user(
            UserCreate(
                username="g2a",
                email="<EMAIL>",
                password="123456",
                is_active=True,
                is_superuser=False,
                phone="13600000002",
                alias="供应商2管理员"
            )
        )
        supplier2_user = await user_controller.get_by_username("g2a")
        supplier2_user.dept_id = supplier_2.id
        await supplier2_user.save()
        await supplier2_user.roles.add(supplier_role)


        # 初始化食材
        food_stuff_exist = await FoodStuff.exists()
        if not food_stuff_exist:
            # 获取单位ID
            kg_unit = await Unit.filter(unit_name="公斤").first()
            g_unit = await Unit.filter(unit_name="克").first()
            jin_unit = await Unit.filter(unit_name="斤").first()
            piece_unit = await Unit.filter(unit_name="个").first()
            pack_unit = await Unit.filter(unit_name="包").first()


            # 创建6个食材
            # 大宗商品 - 供应商1
            await FoodStuff.create(
                food_stuff_name="大米",
                food_stuff_type=FoodStuffType.BULK_COMMODITY,
                food_stuff_unit_id=kg_unit.id,
                supplier_id=supplier_1.id,
                nutrient_info=[
                ],
                food_stuff_price=5.5,
                production_date=datetime(2023, 10, 1),
                shelf_life=365
            )

            # 大宗商品 - 供应商2
            await FoodStuff.create(
                food_stuff_name="面粉",
                food_stuff_type=FoodStuffType.BULK_COMMODITY,
                food_stuff_unit_id=kg_unit.id,
                supplier_id=supplier_2.id,
                nutrient_info=[
                ],
                food_stuff_price=4.8,
                production_date=datetime(2023, 11, 15),
                shelf_life=300
            )

            # 原辅材料 - 供应商1
            await FoodStuff.create(
                food_stuff_name="食用油",
                food_stuff_type=FoodStuffType.RAW_MATERIAL,
                food_stuff_unit_id=jin_unit.id,
                supplier_id=supplier_1.id,
                nutrient_info=[
                ],
                food_stuff_price=15.5,
                production_date=datetime(2023, 9, 20),
                shelf_life=540
            )

            # 原辅材料 - 供应商2
            await FoodStuff.create(
                food_stuff_name="食盐",
                food_stuff_type=FoodStuffType.RAW_MATERIAL,
                food_stuff_unit_id=pack_unit.id,
                supplier_id=supplier_2.id,
                nutrient_info=[],
                food_stuff_price=3.0,
                production_date=datetime(2023, 8, 10),
                shelf_life=720
            )

            # 散货食材 - 供应商1
            await FoodStuff.create(
                food_stuff_name="胡萝卜",
                food_stuff_type=FoodStuffType.LOOSE_FOOD,
                food_stuff_unit_id=jin_unit.id,
                supplier_id=supplier_1.id,
                nutrient_info=[
                ],
                food_stuff_price=3.8,
                production_date=datetime(2024, 3, 15),
                shelf_life=15
            )

            # 散货食材 - 供应商2
            await FoodStuff.create(
                food_stuff_name="西红柿",
                food_stuff_type=FoodStuffType.LOOSE_FOOD,
                food_stuff_unit_id=jin_unit.id,
                supplier_id=supplier_2.id,
                nutrient_info=[
                ],
                food_stuff_price=4.2,
                production_date=datetime(2024, 3, 20),
                shelf_life=10
            )


async def init_data():
    await init_db()
    await init_superuser()
    await init_menus()
    await init_apis()
    await init_roles()
    await init_units()
    await init_demo_departments_roles_users_and_food_stuff()
