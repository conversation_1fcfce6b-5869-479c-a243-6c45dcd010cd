import hashlib
from fastapi import APIRouter, Body, Query

from app.controllers.dept import dept_controller
from app.schemas import Success, Fail, SuccessExtra
from app.schemas.depts import *

router = APIRouter()


@router.get("/list", summary="查看部门列表")
async def list_dept(
    name: str = Query(None, description="部门名称"),
    phone: str = Query(None, description="电话号码"),
    type: int = Query(None, description="部门类型"),
):
    dept_tree = await dept_controller.get_dept_tree(name, phone, type)
    return Success(data=dept_tree)


@router.get("/get", summary="查看部门")
async def get_dept(
    id: int = Query(..., description="部门ID"),
):
    dept_obj = await dept_controller.get(id=id)
    data = await dept_obj.to_dict()
    return Success(data=data)


@router.post("/create", summary="创建部门")
async def create_dept(
    dept_in: DeptCreate,
):
    try:
        await dept_controller.create_dept(obj_in=dept_in)
        return Success(msg="Created Successfully")
    except Exception as e:
        #如果包含exists，则返回400
        if "exists" in str(e):
            return Fail(msg="部门已存在")
        return Fail(msg=str(e))


@router.post("/update", summary="更新部门")
async def update_dept(
    dept_in: DeptUpdate,
):
    await dept_controller.update_dept(obj_in=dept_in)
    return Success(msg="更新成功")


@router.delete("/delete", summary="删除部门")
async def delete_dept(
    dept_id: int = Query(..., description="部门ID"),
):
    await dept_controller.delete_dept(dept_id=dept_id)
    return Success(msg="删除成功")


class AddSupplier(BaseModel):
    supplier_id: int

@router.post("/supplier/add", summary="添加供应商")
async def add_supplier(
    add_supplier: AddSupplier   ,
):
    await dept_controller.add_supplier(supplier_id=add_supplier.supplier_id)
    return Success(msg="添加成功")

@router.get("/suppliers", summary="获取供应商列表")
async def list_suppliers(
    name: str = Query(None, description="供应商名称"),
):
    suppliers = await dept_controller.get_suppliers(name)
    return Success(data=suppliers)

@router.get("/supplier/filter", summary="搜索供应商")
async def filter_suppliers(
    name: str = Query(None, description="供应商名称"),
):
    suppliers = await dept_controller.filter_suppliers(name)
    return Success(data=suppliers)


@router.post("/supplier/remove", summary="移除供应商")
async def remove_supplier(
    supplier_id: int = Query(..., description="供应商ID"),
):
    await dept_controller.remove_supplier(supplier_id=supplier_id)
    return Success(msg="移除成功")


class GenerateToken(BaseModel):
    school_id: int

@router.post("/school/generate-token", summary="生成学校token")
async def generate_school_token(
    generate_token: GenerateToken,
):
    token = await dept_controller.generate_school_token(school_id=generate_token.school_id)
    return Success(data=token)


@router.get("/class/list", summary="获取班级列表")
async def list_classes(
    name: str = Query(None, description="班级名称"),
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
):
    try:
        classes = await dept_controller.get_class_list(name)
        # 分页处理
        total = len(classes)
        start = (page - 1) * page_size
        end = start + page_size
        classes = classes[start:end]
        return SuccessExtra(data=classes, total=total, page=page, page_size=page_size)
    except Exception as e:
        return Fail(msg=str(e))


class UpdateStudentCount(BaseModel):
    class_id: int = Field(..., description="class_id", example=1)
    student_count: int = Field(..., description="学生数量", example=30)

@router.post("/student-count/update", summary="更新学生数量")
async def update_student_count(
    data: UpdateStudentCount,
):
    try:
        await dept_controller.update_student_count_by_class_id(class_id=data.class_id, student_count=data.student_count)
        return Success(msg="更新成功")
    except Exception as e:
        return Fail(msg=str(e))


@router.get("/total-student-count", summary="获取学生总数")
async def get_total_student_count():
    try:
        total_count = await dept_controller.get_total_student_count()
        return Success(data={"total_count": total_count})
    except Exception as e:
        return Fail(msg=str(e))

@router.post("/class/login", summary="班级登录")
async def class_login(
    sign: str = Body(..., description="签名"),
    phone: str = Body(..., description="班主任手机号"),
    t: str = Body(..., description="时间戳"),
):
    #(phone + t + secret_key + "sec")md5
    secret_key = "sleuFDUOAJFDSjfkdui132ds"
    calculate_sign = hashlib.md5((phone + t + secret_key + "sec").encode()).hexdigest()
    if calculate_sign != sign:
        return Fail(msg="签名错误")
    try:
        class_obj = await dept_controller.class_login(phone=phone)
        return SuccessExtra(data=class_obj.to_dict(), msg="登录成功")
    except Exception as e:
        return Fail(msg=str(e))