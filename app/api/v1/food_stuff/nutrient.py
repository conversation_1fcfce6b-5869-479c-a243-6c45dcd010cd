from fastapi import APIRouter, Query
from typing import Optional
from tortoise.expressions import Q

from app.controllers.food_stuff import nutrient_controller
from app.schemas.base import Success, SuccessExtra
from app.schemas.food_stuff import NutrientCreate, NutrientUpdate

router = APIRouter()


@router.get("/list", summary="查看营养素列表")
async def list_nutrient(
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    nutrient_name: str = Query("", description="营养素名称"),
):
    # 构建查询条件
    q = Q()
    if nutrient_name:
        q &= Q(nutrient_name__contains=nutrient_name)
    
    # 查询数据
    total, nutrient_objs = await nutrient_controller.list(page=page, page_size=page_size, search=q)
    
    # 返回结果
    data = [await obj.to_dict() for obj in nutrient_objs]
    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


@router.get("/get", summary="获取营养素详情")
async def get_nutrient(
    id: int = Query(..., description="营养素ID"),
):
    nutrient_obj = await nutrient_controller.get(id=id)
    data = await nutrient_obj.to_dict()
    return Success(data=data)


@router.post("/create", summary="创建营养素")
async def create_nutrient(
    nutrient_in: NutrientCreate,
):
    await nutrient_controller.create(obj_in=nutrient_in)
    return Success(msg="Created Successfully")


@router.post("/update", summary="更新营养素")
async def update_nutrient(
    nutrient_in: NutrientUpdate,
):
    await nutrient_controller.update(id=nutrient_in.id, obj_in=nutrient_in)
    return Success(msg="Updated Successfully")


@router.delete("/delete", summary="删除营养素")
async def delete_nutrient(
    id: int = Query(..., description="营养素ID"),
):
    await nutrient_controller.remove(id=id)
    return Success(msg="Deleted Successfully") 