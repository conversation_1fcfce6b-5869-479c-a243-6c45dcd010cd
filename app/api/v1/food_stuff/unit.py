from fastapi import APIRouter, Query
from typing import Optional
from tortoise.expressions import Q

from app.controllers.food_stuff import unit_controller
from app.schemas.base import Success, SuccessExtra
from app.schemas.food_stuff import UnitCreate, UnitUpdate

router = APIRouter()


@router.get("/list", summary="查看单位列表")
async def list_unit(
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    unit_name: str = Query("", description="单位名称", alias="unitName"),
):
    # 构建查询条件
    q = Q()
    if unit_name:
        q &= Q(unit_name__contains=unit_name)
    
    # 查询数据
    total, unit_objs = await unit_controller.list(page=page, page_size=page_size, search=q)
    
    # 返回结果
    data = [await obj.to_dict() for obj in unit_objs]
    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


@router.get("/get", summary="获取单位详情")
async def get_unit(
    id: int = Query(..., description="单位ID"),
):
    unit_obj = await unit_controller.get(id=id)
    data = await unit_obj.to_dict()
    return Success(data=data)


@router.post("/create", summary="创建单位")
async def create_unit(
    unit_in: UnitCreate,
):
    await unit_controller.create(obj_in=unit_in)
    return Success(msg="Created Successfully")


@router.post("/update", summary="更新单位")
async def update_unit(
    unit_in: UnitUpdate,
):
    await unit_controller.update(id=unit_in.id, obj_in=unit_in)
    return Success(msg="Updated Successfully")


@router.delete("/delete", summary="删除单位")
async def delete_unit(
    id: int = Query(..., description="单位ID"),
):
    await unit_controller.remove(id=id)
    return Success(msg="Deleted Successfully") 