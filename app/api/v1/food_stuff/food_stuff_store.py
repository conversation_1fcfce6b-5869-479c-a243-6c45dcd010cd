from fastapi import APIRouter, Query, HTTPException, Body

from app.controllers.food_stuff_store import food_stuff_store_controller
from app.schemas import Success, Fail
from app.schemas.food_stuff_store import FoodStuffStoreCreate, FoodStuffStoreUpdate, FoodStuffStoreSearch
from pydantic import BaseModel
from typing import Any

router = APIRouter()


@router.get("/list", summary="获取食材库存列表")
async def list_food_stuff_store(
    current: int = Query(1, description="当前页"),
    size: int = Query(10, description="每页数量"),
    food_stuff_name: str = Query(None, description="食材名称", alias="foodStuffName"),
    food_stuff_ids: str = Query(None, description="食材ID列表，多个用逗号分隔", alias="foodStuffIds"),    
    school_id: int = Query(None, description="学校ID", alias="schoolId"),
):
    search = {
        "current": current,
        "size": size,
        "food_stuff_name": food_stuff_name,
        "food_stuff_ids": food_stuff_ids,
        "school_id": school_id
    }
    data = await food_stuff_store_controller.get_list(search)
    return Success(data=data)


@router.get("/get", summary="获取食材库存详情")
async def get_food_stuff_store(
    id: int = Query(..., description="库存ID"),
):
    data = await food_stuff_store_controller.get(id=id)
    return Success(data=data)


@router.post("/create", summary="创建食材库存")
async def create_food_stuff_store(
    store_in: FoodStuffStoreCreate,
):
    await food_stuff_store_controller.create_store(obj_in=store_in)
    return Success(msg="创建成功")


@router.post("/update", summary="更新食材库存")
async def update_food_stuff_store(
    store_in: FoodStuffStoreUpdate = None,
):

    await food_stuff_store_controller.update_store(obj_in=store_in)
    return Success(msg="更新成功")


@router.delete("/delete", summary="删除食材库存")
async def delete_food_stuff_store(
    id: int = Query(..., description="库存ID"),
):
    await food_stuff_store_controller.delete(id=id)
    return Success(msg="删除成功")


# class ConfirmReceive(BaseModel):
#     order_id: int
#     token: str

# @router.post("/confirm-receive", summary="确认入库")
# async def confirm_receive(
#     receive: ConfirmReceive = Body(..., description="入库信息"),
# ):
#     try:
#         await food_stuff_store_controller.confirm_receive(order_id=receive.order_id, token=receive.token)
#         return Success(msg="入库成功")
#     except Exception as e:
#         return Fail(msg=str(e)) 