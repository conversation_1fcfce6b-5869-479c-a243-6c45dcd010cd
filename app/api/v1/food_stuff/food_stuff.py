from fastapi import APIRouter, Query
from typing import Optional
from tortoise.expressions import Q

from app.controllers.food_stuff import food_stuff_controller
from app.schemas.base import Success, SuccessExtra
from app.schemas.food_stuff import FoodStuffCreate, FoodStuffUpdate

router = APIRouter()


@router.get("/list", summary="查看食材列表")
async def list_food_stuff(
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    food_stuff_name: str = Query("", description="食材名称", alias="foodStuffName"),
    food_stuff_type: Optional[int] = Query(None, description="食材类型", alias="foodStuffType"),
):
    # 构建查询条件
    q = Q()
    if food_stuff_name:
        q &= Q(food_stuff_name__contains=food_stuff_name)
    if food_stuff_type:
        q &= Q(food_stuff_type=food_stuff_type)
    
    # 查询数据
    total, items = await food_stuff_controller.list(page=page, page_size=page_size, search=q)
    # 处理数据，添加关联信息
    data = []
    for item in items:
        item_dict = await item.to_dict()
        item_dict['unit_name'] = item.unit_name
        data.append(item_dict)
    
    # 返回结果
    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


@router.get("/get", summary="获取食材详情")
async def get_food_stuff(
    id: int = Query(..., description="食材ID"),
):
    food_stuff_obj = await food_stuff_controller.get(id=id)
    data = await food_stuff_obj.to_dict()
    return Success(data=data)


@router.post("/create", summary="创建食材")
async def create_food_stuff(
    food_stuff_in: FoodStuffCreate,
):
    await food_stuff_controller.create(obj_in=food_stuff_in)
    return Success(msg="Created Successfully")


@router.post("/update", summary="更新食材")
async def update_food_stuff(
    food_stuff_in: FoodStuffUpdate,
):
    await food_stuff_controller.update(id=food_stuff_in.id, obj_in=food_stuff_in)
    return Success(msg="Updated Successfully")


@router.delete("/delete", summary="删除食材")
async def delete_food_stuff(
    id: int = Query(..., description="食材ID"),
):
    await food_stuff_controller.remove(id=id)
    return Success(msg="Deleted Successfully") 