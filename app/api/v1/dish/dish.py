from fastapi import APIRouter, Query, Form
from typing import Optional

from app.controllers.dish import dish_controller
from app.schemas import Success, SuccessExtra
from app.schemas.dish import DishCreate, DishUpdate, DishSearch

router = APIRouter()


@router.get("/list", summary="获取菜品列表")
async def list_dishes(
    current: int = Query(1, description="当前页"),
    size: int = Query(10, description="每页数量"),
    dish_name: Optional[str] = Query(None, description="菜品名称", alias="dishName"),
    is_active: Optional[bool] = Query(None, description="是否启用", alias="isActive"),
):
    search = DishSearch(
        current=current,
        size=size,
        dish_name=dish_name,
        is_active=is_active
    )
    result = await dish_controller.get_list(search)
    return SuccessExtra(
        data=result["items"],
        total=result["total"],
        page=result["page"],
        page_size=result["page_size"]
    )


@router.get("/get", summary="获取菜品详情")
async def get_dish(
    id: int = Query(..., description="菜品ID"),
):
    dish = await dish_controller.get(id=id)
    data = await dish.to_dict()
    return Success(data=data)


@router.post("/create", summary="创建菜品")
async def create_dish(
    dish_in: DishCreate,
):
    await dish_controller.create_dish(obj_in=dish_in)
    return Success(msg="创建成功")


@router.post("/update", summary="更新菜品")
async def update_dish(
    dish_in: DishUpdate,
):
    await dish_controller.update_dish(obj_in=dish_in)
    return Success(msg="更新成功")


@router.delete("/delete", summary="删除菜品")
async def delete_dish(
    id: int = Query(..., description="菜品ID"),
):
    await dish_controller.delete_dish(dish_id=id)
    return Success(msg="删除成功")
