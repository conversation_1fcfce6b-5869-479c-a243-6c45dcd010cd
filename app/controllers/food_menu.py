from tortoise.expressions import Q
from tortoise.transactions import atomic

from app.core.crud import CRUDBase
from app.models.food_menu import FoodMenuOneStudent
from app.schemas.food_menu import FoodMenuOneStudentCreate, FoodMenuOneStudentUpdate, FoodMenuOneStudentSearch
from app.controllers.utils import get_school_id
from app.core.ctx import CTX_USER_ID


class FoodMenuController(CRUDBase[FoodMenuOneStudent, FoodMenuOneStudentCreate, FoodMenuOneStudentUpdate]):
    def __init__(self):
        super().__init__(model=FoodMenuOneStudent)

    async def get_food_menu_list(self, search: FoodMenuOneStudentSearch):
        """获取食谱菜单列表"""
        # 获取当前用户的学校ID
        try:
            school_id = await get_school_id()
        except Exception as e:
            # 如果不是学校用户，则返回空列表
            return {
                "total": 0,
                "data": []
            }
            
        q = Q(school_id=school_id)
        if search.food_menu_name:
            q &= Q(food_menu_name__contains=search.food_menu_name)
        if search.meal_type:
            q &= Q(meal_type=search.meal_type)
        if search.week_day:
            q &= Q(week_day=search.week_day)
        
        # 分页查询
        total = await self.model.filter(q).count()
        data = await self.model.filter(q).limit(search.size).offset((search.current - 1) * search.size)
        
        result = []
        for item in data:
            item_dict = await item.to_dict()
            result.append(item_dict)
        
        return {
            "total": total,
            "data": result
        }
    
    @atomic()
    async def create_food_menu(self, obj_in: FoodMenuOneStudentCreate):
        """创建食谱菜单"""
        # 获取当前用户的学校ID
        school_id = await get_school_id()
        
        # 创建新的食谱菜单对象
        new_menu = FoodMenuOneStudent(
            food_menu_name=obj_in.food_menu_name,
            week_day=obj_in.week_day,
            meal_type=obj_in.meal_type,
            food_stuff_list=obj_in.food_stuff_list,
            school_id=school_id,
            is_active=True if obj_in.is_active is None else obj_in.is_active
        )
        
        # 保存到数据库
        await new_menu.save()
        return new_menu
    
    @atomic()
    async def update_food_menu(self, id: int, obj_in: FoodMenuOneStudentUpdate):
        """更新食谱菜单"""
        # 获取当前用户的学校ID
        school_id = await get_school_id()
        
        # 确保只能更新当前学校的菜单
        food_menu = await self.get(id=id, school_id=school_id)
        if not food_menu:
            raise ValueError("菜单不存在或不属于当前学校")
            
        # 更新菜单信息，忽略school_id字段
        update_data = obj_in.model_dump(exclude_unset=True, exclude={"school_id"})
        for field, value in update_data.items():
            setattr(food_menu, field, value)
        
        await food_menu.save()
        return food_menu
    
    @atomic()
    async def delete_food_menu(self, id: int):
        """删除食谱菜单"""
        # 获取当前用户的学校ID
        school_id = await get_school_id()
        
        # 确保只能删除当前学校的菜单
        food_menu = await self.model.filter(id=id, school_id=school_id).first()
        if not food_menu:
            raise ValueError("菜单不存在或不属于当前学校")
            
        await food_menu.delete()
    
    async def get_food_menu_by_id(self, id: int):
        """根据ID获取食谱菜单"""
        # 获取当前用户的学校ID
        school_id = await get_school_id()
        
        # 确保只能查看当前学校的菜单
        food_menu = await self.model.filter(id=id, school_id=school_id).first()
        if not food_menu:
            raise ValueError("菜单不存在或不属于当前学校")
            
        return food_menu


food_menu_controller = FoodMenuController() 