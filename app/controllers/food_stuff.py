from fastapi import HTTPException
from tortoise.expressions import Q

from app.core.crud import CRUDBase
from app.core.ctx import CTX_USER_ID
from app.controllers.user import user_controller
from app.models.admin import Cooperators, Dept
from app.models.food_stuff import FoodStuff, Unit, Nutrient
from app.schemas.depts import DeptType
from app.schemas.food_stuff import (
    FoodStuffCreate, FoodStuffUpdate, FoodStuffSearch,
    UnitCreate, UnitUpdate,
    NutrientCreate, NutrientUpdate
)
from app.controllers.utils import get_school_id


class FoodStuffController(CRUDBase[FoodStuff, FoodStuffCreate, FoodStuffUpdate]):
    async def get_list(self, params: FoodStuffSearch):
        # 构建查询条件
        q = Q()
        if params.foodStuffName:
            q &= Q(food_stuff_name__contains=params.foodStuffName)
        if params.foodStuffType:
            q &= Q(food_stuff_type=params.foodStuffType)
        
        # 获取总数
        total = await self.model.filter(q).count()
        
        # 获取分页数据
        items = await self.model.filter(q).offset((params.current - 1) * params.size).limit(params.size)
        # 获取所有单位信息
        units = await Unit.all()
        units_dict = {unit.id: unit.unit_name for unit in units}
        print(units_dict)
        # 为每个食材添加单位信息
        for item in items:
            if item.food_stuff_unit_id:
                item.unit_name = units_dict.get(item.food_stuff_unit_id, "")
            else:
                item.unit_name = ""
        
        # 转换为字典列表
        data = []
        for item in items:
            item_dict = await item.to_dict()
            # 获取单位名称
            # unit = await Unit.get_or_none(id=item.food_stuff_unit_id)
            # if unit:
            #     item_dict["unit_name"] = unit.unit_name
            # else:
            #     item_dict["unit_name"] = ""
            
            # 获取供应商名称 (如果有供应商模型)
            # supplier = await Supplier.get_or_none(id=item.supplier_id)
            # if supplier:
            #     item_dict["supplier_name"] = supplier.name
            # else:
            #     item_dict["supplier_name"] = ""
            
            data.append(item_dict)
        
        return {"data": data, "total": total}
    
    async def list(self, *, page: int = 1, page_size: int = 10, search: Q = None):
        if search is None:
            search = Q()
        # 获取当前用户所属学校
        school_id = await get_school_id()

        # 获取供应商列表
        suppliers = await Cooperators.filter(school_id=school_id).values_list("supplier_id", flat=True)
        # 获取总数
        total = await self.model.filter(search, supplier_id__in=suppliers).count()
        
        # 获取分页数据
        items = await self.model.filter(search, supplier_id__in=suppliers).offset((page - 1) * page_size).limit(page_size)
        
        # 获取所有单位信息
        units = await Unit.all()
        units_dict = {unit.id: unit.unit_name for unit in units}
        # 为每个食材添加单位信息
        for item in items:
            if item.food_stuff_unit_id:
                item.unit_name = units_dict.get(item.food_stuff_unit_id, "")
            else:
                item.unit_name = ""
        # 为每个项目添加额外信息
        # for item in items:
        #     # 获取单位名称
        #     if hasattr(item, 'food_stuff_unit_id'):
        #         unit = await Unit.get_or_none(id=item.food_stuff_unit_id)
        #         if unit:
        #             setattr(item, 'unit_name', unit.unit_name)
                    
        #     获取供应商名称 (如果有供应商模型)
        #     if hasattr(item, 'supplier_id'):
        #         supplier = await Supplier.get_or_none(id=item.supplier_id)
        #         if supplier:
        #             setattr(item, 'supplier_name', supplier.name)
        
        return total, items
    
    async def create(self, *, obj_in: FoodStuffCreate):
        #如果名字和supplier_id都相同，则不创建
        if await self.model.filter(food_stuff_name=obj_in.food_stuff_name, supplier_id=obj_in.supplier_id).exists():
            raise HTTPException(status_code=400, detail="食材已存在")
        # 创建食材
        return await super().create(obj_in=obj_in)
    
    async def update(self, *, id: int, obj_in: FoodStuffUpdate):
        # 更新食材
        obj = await self.get(id=id)
        update_data = obj_in.model_dump(exclude_unset=True, exclude={"id"})
        obj.update_from_dict(update_data)
        await obj.save()
        return obj
    
    async def delete(self, *, id: int):
        # 删除食材
        obj = await self.get(id=id)
        await obj.delete()
        return True


class UnitController(CRUDBase[Unit, UnitCreate, UnitUpdate]):
    async def list(self, *, page: int = 1, page_size: int = 10, search: Q = None):
        if search is None:
            search = Q()
            
        # 获取总数
        total = await self.model.filter(search).count()
        
        # 获取分页数据
        items = await self.model.filter(search).offset((page - 1) * page_size).limit(page_size)
        
        return total, items


class NutrientController(CRUDBase[Nutrient, NutrientCreate, NutrientUpdate]):
    async def list(self, *, page: int = 1, page_size: int = 10, search: Q = None):
        if search is None:
            search = Q()
            
        # 获取总数
        total = await self.model.filter(search).count()
        
        # 获取分页数据
        items = await self.model.filter(search).offset((page - 1) * page_size).limit(page_size)
        
        return total, items


# 实例化控制器
food_stuff_controller = FoodStuffController(FoodStuff)
unit_controller = UnitController(Unit)
nutrient_controller = NutrientController(Nutrient) 