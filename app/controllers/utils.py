from app.core.ctx import CTX_USER_ID
from app.models.admin import DeptType
from app.controllers.user import user_controller
from app.models.admin import Dept

async def get_school_id():
    # 获取当前用户所属部门
    user_id = CTX_USER_ID.get()
    user_obj = await user_controller.get(id=user_id)
    if not user_obj or not user_obj.dept_id:
        raise Exception("用户没有所属部门")
    
    # 获取用户所属部门信息
    user_dept = await Dept.filter(id=user_obj.dept_id).first()
    
    # 检查部门类型是否为学校
    if user_dept.type != DeptType.SCHOOL:
        raise Exception("当前用户不是学校")
    
    # 获取学校ID
    school_id = user_dept.id
    return school_id


async def get_supplier_id():
    # 获取当前用户所属部门
    user_id = CTX_USER_ID.get()
    user_obj = await user_controller.get(id=user_id)
    if not user_obj or not user_obj.dept_id:
        raise Exception("用户没有所属部门")
    
    # 获取用户所属部门信息
    user_dept = await Dept.filter(id=user_obj.dept_id).first()
    
    # 检查部门类型是否为学校
    if user_dept.type != DeptType.SUPPLY:
        raise Exception("当前用户不是供应商")
    
    # 获取供应商ID
    supplier_id = user_dept.id
    return supplier_id