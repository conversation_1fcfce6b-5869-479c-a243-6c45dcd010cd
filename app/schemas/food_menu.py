from typing import Annotated, Optional

from pydantic import BaseModel, Field


class FoodMenuOneStudentBase(BaseModel):
    food_menu_name: Annotated[str | None, Field(alias="foodMenuName", title="菜单名称")] = None
    meal_type: Annotated[int | None, Field(alias="mealType", title="餐次类型")] = None
    week_day: Annotated[int | None, Field(alias="weekDay", title="星期几")] = None
    food_stuff_list: Annotated[list | None, Field(alias="foodStuffList", title="食材列表")] = None
    is_active: Annotated[bool | None, Field(alias="isActive", title="是否启用")] = True

    class Config:
        populate_by_name = True


class FoodMenuOneStudentCreate(FoodMenuOneStudentBase):
    food_menu_name: Annotated[str, Field(alias="foodMenuName", title="菜单名称")]
    meal_type: Annotated[int, Field(alias="mealType", title="餐次类型")]
    week_day: Annotated[int, Field(alias="weekDay", title="星期几")]
    food_stuff_list: Annotated[list, Field(alias="foodStuffList", title="食材列表")]


class FoodMenuOneStudentUpdate(FoodMenuOneStudentBase):
    ...


class FoodMenuOneStudentSearch(FoodMenuOneStudentBase):
    current: Annotated[int | None, Field(description="页码")] = 1
    size: Annotated[int | None, Field(description="每页数量")] = 10



__all__ = [
    "FoodMenuOneStudentBase", "FoodMenuOneStudentCreate", "FoodMenuOneStudentUpdate", "FoodMenuOneStudentSearch",
] 