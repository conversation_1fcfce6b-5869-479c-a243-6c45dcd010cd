from typing import Annotated, List, Dict, Any

from pydantic import BaseModel, Field


class IngredientItem(BaseModel):
    food_stuff_id: Annotated[int, Field(alias="foodStuffId", title="食材ID")]
    food_stuff_name: Annotated[str, Field(alias="foodStuffName", title="食材名称")]
    unit_name: Annotated[str, Field(alias="unitName", title="计量单位")]
    quantity: Annotated[float, Field(alias="quantity", title="数量")]

    class Config:
        populate_by_name = True


class DishBase(BaseModel):
    dish_name: Annotated[str | None, Field(alias="dishName", title="菜品名称")] = None
    ingredients: Annotated[List[IngredientItem] | None, Field(alias="ingredients", title="食材列表")] = None
    is_active: Annotated[bool | None, Field(alias="isActive", title="是否启用")] = True

    class Config:
        populate_by_name = True


class DishCreate(DishBase):
    dish_name: Annotated[str, Field(alias="dishName", title="菜品名称")]
    ingredients: Annotated[List[IngredientItem], Field(alias="ingredients", title="食材列表")]


class DishUpdate(DishBase):
    id: Annotated[int, Field(alias="id", title="菜品ID")]

    def update_dict(self):
        return self.model_dump(exclude_unset=True, exclude={"id"})


class DishSearch(DishBase):
    current: Annotated[int | None, Field(description="页码")] = 1
    size: Annotated[int | None, Field(description="每页数量")] = 10


__all__ = ["DishBase", "DishCreate", "DishUpdate", "DishSearch", "IngredientItem"]
