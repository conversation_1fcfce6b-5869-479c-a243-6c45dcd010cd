from enum import IntEnum
from typing import Optional

from pydantic import BaseModel, Field


class DeptType(IntEnum):
    SCHOOL = 1 # 学校
    SUPPLY = 2 # 供应商

class BaseDept(BaseModel):
    name: str = Field(..., description="部门名称", example="研发中心")
    phone: str = Field("", description="电话", example="12345678901")
    type: Optional[DeptType] = Field(..., description="部门类型", example=DeptType.SCHOOL)
    address: str = Field("", description="地址", example="北京市海淀区")
    email: str = Field("", description="邮箱", example="<EMAIL>")
    desc: str = Field("", description="备注", example="研发中心")
    order: int = Field(0, description="排序")
    parent_id: int = Field(0, description="父部门ID")
    token: str = Field("", description="token")


class DeptCreate(BaseDept): ...


class DeptUpdate(BaseDept):
    id: int

    def update_dict(self):
        return self.model_dump(exclude_unset=True, exclude={"id"})


class BaseStudentCount(BaseModel):
    student_count: int = Field(..., description="学生统计", example=100)
    class_info_id: int = Field(..., description="班级ID", example=1)


class StudentCountCreate(BaseStudentCount): ...


class StudentCountUpdate(BaseStudentCount):
    id: int

    def update_dict(self):
        return self.model_dump(exclude_unset=True, exclude={"id"})
