from typing import Annotated, Optional

from pydantic import BaseModel, Field



class FoodStuffStoreBase(BaseModel):
    food_stuff_id: Annotated[int | None, Field(alias="foodStuffId", title="食材ID")] = None
    store_count: Annotated[float | None, Field(alias="storeCount", title="库存数量")] = None
    school_id: Annotated[int | None, Field(alias="schoolId", title="学校ID")] = None

    class Config:
        populate_by_name = True


class FoodStuffStoreCreate(FoodStuffStoreBase):
    food_stuff_id: Annotated[int, Field(alias="foodStuffId", title="食材ID")]
    store_count: Annotated[float, Field(alias="storeCount", title="库存数量")]
    school_id: Annotated[int, Field(alias="schoolId", title="学校ID")]


class FoodStuffStoreUpdate(FoodStuffStoreBase):
    id: Annotated[int, Field(alias="id", title="库存ID")]
    store_count: Annotated[float, Field(alias="storeCount", title="库存数量")]


class FoodStuffStoreSearch(FoodStuffStoreBase):
    current: Annotated[int | None, Field(description="页码")] = 1
    size: Annotated[int | None, Field(description="每页数量")] = 10
    food_stuff_name: Annotated[str | None, Field(description="食材名称")] = None


__all__ = ["FoodStuffStoreBase", "FoodStuffStoreCreate", "FoodStuffStoreUpdate", "FoodStuffStoreSearch"] 