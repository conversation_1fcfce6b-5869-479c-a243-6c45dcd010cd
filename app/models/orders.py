from enum import IntEnum
from tortoise import fields

from .base import BaseModel, TimestampMixin

class OrderStatus(IntEnum):
    PENDING = 1  # 待处理
    SHIPPED = 2  # 已出库
    CONFIRMED = 3  # 已入库
    PAID = 4  # 已支付
    CANCELLED = 5  # 已取消

class OrderType(IntEnum):
    BULK_COMMODITY = 1  # 大宗商品
    RAW_MATERIAL = 2  # 原辅材料
    LOOSE_FOOD = 3  # 散货食材

class Order(BaseModel, TimestampMixin):
    id = fields.IntField(pk=True, description="订单id")
    order_type = fields.IntEnumField(enum_type=OrderType, description="订单类型")
    order_status = fields.IntEnumField(enum_type=OrderStatus, default=OrderStatus.PENDING, description="订单状态")
    order_number = fields.CharField(max_length=20, description="订单编号", index=True, unique=True)
    order_paid_date = fields.DatetimeField(description="订单支付时间", null=True, blank=True)
    order_shipped_date = fields.DatetimeField(description="订单出库时间", null=True, blank=True)
    order_confirm_date = fields.DatetimeField(description="订单确认入库时间", null=True, blank=True)
    supplier_id = fields.IntField(description="订单供应商ID")
    school_id = fields.IntField(description="订单来源学校ID")
    order_remark = fields.TextField(description="订单备注")
    order_items = fields.JSONField(description="订单商品")
    order_total = fields.FloatField(description="订单总金额", default=0)

    class Meta:
        table = "order"
        table_description = "订单"
