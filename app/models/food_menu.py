from tortoise import fields
from enum import Enum, IntEnum
from .base import BaseModel, TimestampMixin

class WeekDay(IntEnum):
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

class MealType(IntEnum):
    BREAKFAST = 1
    MORNING_SNACK = 2
    LUNCH = 3
    AFTERNOON_SNACK = 4
    DINNER = 5
    

class FoodMenuOneStudent(BaseModel, TimestampMixin):
    id = fields.IntField(pk=True, description="食材菜单id")
    food_menu_name = fields.CharField(max_length=20, description="食材菜单名称")
    week_day = fields.IntEnumField(WeekDay, description="星期几")
    meal_type = fields.IntEnumField(MealType, description="餐次类型")
    food_stuff_list = fields.JSONField(description="食材列表")#[{"food_stuff_id": "1", "food_stuff_name": "土豆", "food_stuff_unit": "克", "food_stuff_count": 100}]
    is_active = fields.BooleanField(default=True, description="是否启用")
    school_id = fields.IntField(description="学校ID", index=True)

    class Meta:
        table = "food_menu_one_student"
        table_description = "菜单配料"
