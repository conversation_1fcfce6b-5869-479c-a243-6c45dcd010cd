from tortoise import fields

from .base import BaseModel, TimestampMixin


class Dish(BaseModel, TimestampMixin):
    id = fields.IntField(pk=True, description="菜品ID")
    dish_name = fields.CharField(max_length=50, description="菜品名称", index=True)
    ingredients = fields.JSONField(description="食材列表")  # [{"food_stuff_id": 1, "food_stuff_name": "土豆", "unit_name": "克", "quantity": 100}]
    is_active = fields.BooleanField(default=True, description="是否启用")
    school_id = fields.IntField(description="学校ID", index=True)

    class Meta:
        table = "dish"
        table_description = "菜品"
