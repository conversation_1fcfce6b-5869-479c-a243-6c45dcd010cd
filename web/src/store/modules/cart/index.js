import { defineStore } from 'pinia'
import { lStorage } from '@/utils'

export const useCartStore = defineStore('cart', {
  state() {
    return {
      // 从本地存储加载购物车数据，如果没有则使用空数组
      cartItems: lStorage.get('cartItems') || [],
    }
  },
  getters: {
    // 购物车商品总数
    cartCount() {
      return this.cartItems.reduce((sum, item) => sum + item.quantity, 0)
    },
    
    // 购物车商品详情（带有单位和供应商信息）
    cartItemsWithDetails() {
      return this.cartItems
    }
  },
  actions: {
    // 添加商品到购物车
    addToCart(foodStuff, unitOptions, supplierOptions) {
      const existingItem = this.cartItems.find(item => item.id === foodStuff.id)
      
      if (existingItem) {
        existingItem.quantity++
      } else {
        // 添加新商品
        const unitName = unitOptions.find(unit => unit.value === foodStuff.food_stuff_unit_id)?.label || '未知单位'
        const supplierName = supplierOptions.find(supplier => supplier.value === foodStuff.supplier_id)?.label || '未知供应商'
        
        this.cartItems.push({
          id: foodStuff.id,
          name: foodStuff.food_stuff_name,
          quantity: 1,
          type: foodStuff.food_stuff_type,
          unitId: foodStuff.food_stuff_unit_id,
          unitName: unitName,
          supplierId: foodStuff.supplier_id,
          supplierName: supplierName,
        })
      }
      
      // 保存到本地存储
      this.saveToLocalStorage()
    },
    
    // 更新购物车商品数量
    updateItemQuantity(itemId, delta) {
      const cartItem = this.cartItems.find(item => item.id === itemId)
      
      if (cartItem) {
        cartItem.quantity += delta
        if (cartItem.quantity <= 0) {
          // 如果数量小于等于0，从购物车中移除
          this.removeFromCart(itemId)
        } else {
          // 保存到本地存储
          this.saveToLocalStorage()
        }
      }
    },
    
    // 从购物车中移除商品
    removeFromCart(itemId) {
      const index = this.cartItems.findIndex(item => item.id === itemId)
      if (index !== -1) {
        this.cartItems.splice(index, 1)
        
        // 保存到本地存储
        this.saveToLocalStorage()
      }
    },
    
    // 清空购物车
    clearCart() {
      this.cartItems = []
      
      // 保存到本地存储
      this.saveToLocalStorage()
    },
    
    // 保存购物车数据到本地存储
    saveToLocalStorage() {
      lStorage.set('cartItems', this.cartItems)
    }
  }
}) 