<script setup>
import { h, onMounted, ref, resolveDirective, withDirectives } from 'vue'
import {
  NButton,
  NForm,
  NFormItem,
  NInput,
  NPopconfirm
} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'

import { formatDate, renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import api from '@/api'
import TheIcon from '@/components/icon/TheIcon.vue'

defineOptions({ name: '营养素管理' })

const $table = ref(null)
const queryItems = ref({})
const vPermission = resolveDirective('permission')

const {
  modalVisible,
  modalAction,
  modalTitle,
  modalLoading,
  handleAdd,
  handleDelete,
  handleEdit,
  handleSave,
  modalForm,
  modalFormRef,
} = useCRUD({
  name: '营养素',
  initForm: {
    nutrientName: ''
  },
  doCreate: api.createNutrient,
  doDelete: api.deleteNutrient,
  doUpdate: api.updateNutrient,
  refresh: () => $table.value?.handleSearch(),
})

onMounted(() => {
  $table.value?.handleSearch()
})

const columns = [
  {
    title: '营养素名称',
    key: 'nutrient_name',
    width: 150,
    align: 'center'
  },
  {
    title: '创建日期',
    key: 'created_at',
    width: 150,
    align: 'center',
    render(row) {
      return h('span', formatDate(row.created_at))
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    align: 'center',
    fixed: 'right',
    render(row) {
      return [
        withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              style: 'margin-right: 8px;',
              onClick: () => {
                handleEdit(row)
              },
            },
            {
              default: () => '编辑',
              icon: renderIcon('material-symbols:edit-outline', { size: 16 }),
            }
          ),
          [[vPermission, 'post/api/v1/nutrient/update']]
        ),
        h(
          NPopconfirm,
          {
            onPositiveClick: () => handleDelete({ id: row.id }, false),
            onNegativeClick: () => {},
          },
          {
            trigger: () =>
              withDirectives(
                h(
                  NButton,
                  {
                    size: 'small',
                    type: 'error',
                    style: 'margin-right: 8px;',
                  },
                  {
                    default: () => '删除',
                    icon: renderIcon('material-symbols:delete-outline', { size: 16 }),
                  }
                ),
                [[vPermission, 'delete/api/v1/nutrient/delete']]
              ),
            default: () => h('div', {}, '确定删除该营养素吗?'),
          }
        )
      ]
    }
  }
]
</script>

<template>
  <CommonPage show-footer title="营养素管理">
    <template #action>
      <NButton v-permission="'post/api/v1/nutrient/create'" type="primary" @click="handleAdd">
        <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />新增营养素
      </NButton>
    </template>

    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="api.getNutrientList"
    >
      <template #queryBar>
        <QueryBarItem label="营养素名称" :label-width="80">
          <NInput
            v-model:value="queryItems.nutrientName"
            clearable
            type="text"
            placeholder="请输入营养素名称"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
      </template>
    </CrudTable>

    <CrudModal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :loading="modalLoading"
      @save="handleSave"
    >
      <NForm
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="modalForm"
        :disabled="modalAction === 'view'"
      >
        <NFormItem
          label="营养素名称"
          path="nutrientName"
          :rule="{
            required: true,
            message: '请输入营养素名称',
            trigger: ['input', 'blur'],
          }"
        >
          <NInput v-model:value="modalForm.nutrientName" placeholder="请输入营养素名称" />
        </NFormItem>
      </NForm>
    </CrudModal>
  </CommonPage>
</template> 