<template>
  <n-card>
    <n-space vertical>
      <n-form
        ref="formRef"
        inline
        :model="searchForm"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="食材名称" path="foodStuffName">
          <n-input v-model:value="searchForm.foodStuffName" placeholder="请输入食材名称" />
        </n-form-item>
        <!-- <n-form-item label="学校" path="schoolId">
          <n-select
            v-model:value="searchForm.schoolId"
            :options="schoolOptions"
            placeholder="请选择学校"
            clearable
          />
        </n-form-item> -->
        <n-form-item>
          <n-button type="primary" @click="handleSearch"> 搜索 </n-button>
          <n-button style="margin-left: 8px" @click="handleReset"> 重置 </n-button>
        </n-form-item>
      </n-form>

      <n-data-table
        :columns="columns"
        :data="tableData"
        :pagination="pagination"
        :loading="loading"
        @update:page="handlePageChange"
      />
    </n-space>
  </n-card>
</template>

<script setup>
import { ref, onMounted, h } from 'vue'
import { useMessage, NInputNumber } from 'naive-ui'
import api from '@/api'

const message = useMessage()
const loading = ref(false)
const tableData = ref([])
const searchForm = ref({
  foodStuffName: '',
  schoolId: null,
  current: 1,
  size: 10
})

const pagination = ref({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 40],
  onChange: (page) => {
    pagination.value.page = page
    searchForm.value.current = page
    getList()
  },
  onUpdatePageSize: (pageSize) => {
    pagination.value.pageSize = pageSize
    pagination.value.page = 1
    searchForm.value.size = pageSize
    searchForm.value.current = 1
    getList()
  }
})

const columns = [
  {
    title: '食材名称',
    key: 'foodStuffName'
  },
  {
    title: '学校名称',
    key: 'schoolName'
  },
  {
    title: '库存数量',
    key: 'storeCount',
    render(row) {
      return h(NInputNumber, {
        value: row.storeCount,
        min: 0,
        precision: 2,
        onUpdateValue: (value) => {
          handleUpdateStoreCount(row.id, value)
        }
      })
    }
  },
  {
    title: '单位',
    key: 'unitName'
  },
  {
    title: '更新时间',
    key: 'updatedAt'
  }
]

const getList = async () => {
  loading.value = true
  try {
    const res = await api.getFoodStuffStoreList({
      food_stuff_name: searchForm.value.foodStuffName,
      school_id: searchForm.value.schoolId,
      current: searchForm.value.current,
      size: searchForm.value.size
    })
    if (res.code === 0) {
      tableData.value = res.data.items
      pagination.value.itemCount = res.data.total
    }
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  searchForm.value.current = 1
  pagination.value.page = 1
  getList()
}

const handleReset = () => {
  searchForm.value = {
    foodStuffName: '',
    current: 1,
    size: 10
  }
  getList()
}

const handlePageChange = (page) => {
  searchForm.value.current = page
  getList()
}

const handleUpdateStoreCount = async (id, value) => {
  try {
    const res = await api.updateFoodStuffStore({
      id: id,
      storeCount: value
    })
    if (res.code === 0) {
      message.success('更新成功')
      getList()
    }
  } catch (error) {
    console.error(error)
    message.error('更新失败')
  }
}

onMounted(() => {
  getList()
})
</script> 