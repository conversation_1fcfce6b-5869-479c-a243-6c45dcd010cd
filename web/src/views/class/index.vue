<template>
  <CommonPage show-footer title="班级管理">
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="api.getClassList"
    >
      <template #queryBar>
        <QueryBarItem label="班级名称" :label-width="70">
          <NInput
            v-model:value="queryItems.name"
            clearable
            type="text"
            placeholder="请输入班级名称"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
      </template>
    </CrudTable>
  </CommonPage>
</template>

<script setup>
import { h, onMounted, ref } from 'vue'
import { NButton, NInput, NInputNumber, useMessage } from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudTable from '@/components/table/CrudTable.vue'

import api from '@/api'

defineOptions({ name: '班级管理' })

const $table = ref(null)
const queryItems = ref({})
const message = useMessage()

const handleUpdateStudentCount = async (classId, value) => {
  try {
    await api.updateStudentCount({
      class_id: classId,
      student_count: value
    })
    message.success('更新成功')
    $table.value?.handleSearch()
  } catch (error) {
    console.error(error)
    message.error('更新失败')
  }
}

const columns = [
  {
    title: '班级ID',
    key: 'id',
    width: 80
  },
  {
    title: '班级名称',
    key: 'name',
    width: 200
  },
  {
    title: '班级描述',
    key: 'desc',
    width: 200
  },
  {
    title: '学生数量',
    key: 'student_count',
    width: 150,
    render(row) {
      return h(NInputNumber, {
        value: row.student_count,
        min: 0,
        precision: 0,
        onUpdateValue: (value) => {
          handleUpdateStudentCount(row.id, value)
        }
      })
    }
  },
  {
    title: '排序',
    key: 'order',
    width: 80
  }
]

onMounted(() => {
  $table.value?.handleSearch()
})
</script>
