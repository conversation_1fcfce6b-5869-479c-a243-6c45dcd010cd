<script setup>
import { h, onMounted, ref, resolveDirective, withDirectives, computed } from 'vue'
import {
  NButton,
  NForm,
  NFormItem,
  NInput,
  NInputNumber,
  NPopconfirm,
  NTag,
  NSelect,
  NSpace,
  NDataTable,
  NBadge,
  NModal,
  NList,
  NListItem,
  NEmpty,
  NCard,
  NInputGroup,
  NStatistic,
  NDivider,
  NDatePicker,
  useMessage
} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'

import { formatDate, renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import api from '@/api'
import TheIcon from '@/components/icon/TheIcon.vue'

defineOptions({ name: '食谱' })

const $message = useMessage()
const $table = ref(null)
const queryItems = ref({})
const vPermission = resolveDirective('permission')

// 下单相关变量
const showOrderModal = ref(false)
const currentMenu = ref(null)
const totalStudentCount = ref(0)
const foodStuffStoreData = ref([])
const orderLoading = ref(false)
const orderQuantities = ref({})

// 打开下单弹窗
async function openOrderModal(row) {
  orderLoading.value = true
  showOrderModal.value = true
  currentMenu.value = row

  try {
    // 获取学生总数
    const studentCountRes = await api.getTotalStudentCount()
    if (studentCountRes && studentCountRes.data) {
      totalStudentCount.value = studentCountRes.data.total_count
    }

    // 从菜品列表中提取所有食材
    const allIngredients = []
    if (row.dish_list && row.dish_list.length > 0) {
      row.dish_list.forEach(dish => {
        if (dish.ingredients && dish.ingredients.length > 0) {
          dish.ingredients.forEach(ingredient => {
            const existingIngredient = allIngredients.find(item => item.food_stuff_id === ingredient.food_stuff_id)
            if (existingIngredient) {
              // 如果食材已存在，累加数量
              existingIngredient.quantity += ingredient.quantity
            } else {
              // 如果食材不存在，添加新的食材
              allIngredients.push({
                food_stuff_id: ingredient.food_stuff_id,
                food_stuff_name: ingredient.food_stuff_name,
                food_stuff_unit: ingredient.unit_name,
                food_stuff_count: ingredient.quantity
              })
            }
          })
        }
      })
    }

    // 获取食材库存
    if (allIngredients.length > 0) {
      const foodStuffIds = allIngredients.map(item => item.food_stuff_id)
      const storeRes = await api.getFoodStuffStoreList({
        food_stuff_ids: foodStuffIds.join(',')
      })

      if (storeRes && storeRes.data) {
        foodStuffStoreData.value = storeRes.data.items

        // 初始化下单数量
        orderQuantities.value = {}
        allIngredients.forEach(item => {
          const required = calculateRequiredAmount(item)
          const store = getStoreAmount(item.food_stuff_id)
          // 计算初始下单数量：需要数量减去库存数量，最小为0
          const initialOrderQuantity = Math.max(0, required - store)
          orderQuantities.value[item.food_stuff_id] = initialOrderQuantity
        })
      }
    }

    // 将合并后的食材列表存储到currentMenu中，用于下单弹窗显示
    currentMenu.value.food_stuff_list = allIngredients

  } catch (error) {
    console.error('获取数据失败:', error)
    $message.error('获取数据失败')
  } finally {
    orderLoading.value = false
  }
}

// 关闭下单弹窗
function closeOrderModal() {
  showOrderModal.value = false
  currentMenu.value = null
}

// 计算需要的食材数量
function calculateRequiredAmount(foodStuffItem) {
  return foodStuffItem.food_stuff_count * totalStudentCount.value
}

// 获取食材库存数量
function getStoreAmount(foodStuffId) {
  const storeItem = foodStuffStoreData.value.find(item => item.food_stuff_id === foodStuffId)
  return storeItem ? storeItem.store_count : 0
}

// 提交订单
async function submitOrder() {
  try {
    // 准备订单数据
    const orderItems = currentMenu.value.food_stuff_list.map(item => {
      // 使用用户设置的下单数量
      const orderQuantity = orderQuantities.value[item.food_stuff_id] || 0
      return {
        id: item.food_stuff_id,
        name: item.food_stuff_name,
        quantity: orderQuantity,
        unitName: item.food_stuff_unit
      }
    }).filter(item => item.quantity > 0) // 只包含数量大于0的项目

    // 如果没有需要下单的食材，提示用户
    if (orderItems.length === 0) {
      $message.warning('没有需要下单的食材')
      return
    }

    const orderData = {
      orderItems: orderItems,
      orderRemark: `根据菜单 ${currentMenu.value.food_menu_name} 自动下单`
    }

    // 调用下单API
    await api.createOrder(orderData)

    // 关闭弹窗
    showOrderModal.value = false
    // 显示成功提示
    $message.success('下单成功')
  } catch (error) {
    console.error('下单失败:', error)
    $message.error('下单失败，请重试')
  }
}


// 日期格式化函数
function formatDateForDisplay(dateValue) {
  if (!dateValue) return ''
  const date = new Date(dateValue)
  return date.toLocaleDateString('zh-CN')
}

// 餐次类型选项
const mealTypeOptions = [
  { label: '早餐', value: 1 },
  { label: '上午加餐', value: 2 },
  { label: '午餐', value: 3 },
  { label: '下午加餐', value: 4 },
  { label: '晚餐', value: 5 }
]

// 菜品列表
const dishOptions = ref([])
// 获取菜品列表
async function fetchDishOptions() {
  try {
    const res = await api.getDishList({ size: 9999 })
    if (res && res.data) {
      dishOptions.value = res.data.map(item => ({
        label: item.dish_name,
        value: item.id,
        ingredients: item.ingredients || []
      }))
    }
  } catch (error) {
    console.error('加载菜品数据失败:', error)
  }
}

// 添加菜品到列表
function addDish() {
  modalForm.value.dishList.push({ dish_id: null, dish_name: '', ingredients: [] })
}

// 移除菜品
function removeDish(index) {
  if (modalForm.value.dishList && modalForm.value.dishList.length > index) {
    modalForm.value.dishList.splice(index, 1)
  }
}

// 菜品选择变更时更新菜品信息
function updateDishInfo(index, dishId) {
  console.log(modalForm.value.dishList)
  if (!modalForm.value.dishList) return

  const selectedDish = dishOptions.value.find(item => item.value === dishId)
  if (selectedDish) {
    const newItem = {
      dish_id: dishId,
      dish_name: selectedDish.label,
      ingredients: selectedDish.ingredients || []
    }
    modalForm.value.dishList.splice(index, 1, newItem)
  }
}

const {
  modalVisible,
  modalAction,
  modalTitle,
  modalLoading,
  handleAdd: originalHandleAdd,
  handleDelete,
  handleEdit: originalHandleEdit,
  handleSave,
  modalForm,
  modalFormRef,
} = useCRUD({
  name: '食谱',
  initForm: {
    foodMenuName: '',
    menuDate: null,
    mealType: 1,
    dishList: []
  },
  doCreate: api.createFoodMenu,
  doDelete: api.deleteFoodMenu,
  doUpdate: api.updateFoodMenu,
  refresh: () => $table.value?.handleSearch(),
})

// 重写handleAdd和handleEdit方法，在打开弹窗前刷新选项数据
const handleAdd = async () => {
  await fetchDishOptions() // 刷新菜品选项数据
  modalForm.value.dishList.push({ dish_id: null, dish_name: '', ingredients: [] })
  console.log(modalForm.value)
  originalHandleAdd()
}

const handleEdit = async (row) => {
  await fetchDishOptions() // 刷新菜品选项数据
  // 将API返回的字段名映射到表单字段名
  const formData = {
    id: row.id,
    foodMenuName: row.food_menu_name,
    menuDate: row.menu_date ? new Date(row.menu_date).getTime() : null,
    mealType: row.meal_type,
    dishList: row.dish_list || []
  }
  originalHandleEdit(formData)
}

// 表格列定义
const columns = [
  {
    title: '菜单名称',
    key: 'food_menu_name',
    width: 180,
    align: 'center',
    ellipsis: { tooltip: true }
  },
  {
    title: '日期',
    key: 'menu_date',
    width: 120,
    align: 'center',
    render(row) {
      return row.menu_date ? formatDateForDisplay(row.menu_date) : '-'
    }
  },
  {
    title: '餐次',
    key: 'meal_type',
    width: 120,
    align: 'center',
    render(row) {
      const mealType = mealTypeOptions.find(item => item.value === row.meal_type)
      return mealType ? mealType.label : row.meal_type
    }
  },
  {
    title: '菜品信息',
    key: 'dish_list',
    align: 'center',
    render(row) {
      if (!row.dish_list || row.dish_list.length === 0) {
        return h('span', '无菜品信息')
      }

      return h(
        NSpace,
        { vertical: true },
        {
          default: () => row.dish_list.map(item => {
            return h(
              NTag,
              { type: 'info', round: true },
              { default: () => item.dish_name }
            )
          })
        }
      )
    }
  },
  {
    title: '状态',
    key: 'is_active',
    width: 100,
    align: 'center',
    render(row) {
      return row.is_active
        ? h(NTag, { type: 'success', round: true }, { default: () => '启用' })
        : h(NTag, { type: 'error', round: true }, { default: () => '停用' })
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 450,
    align: 'center',
    fixed: 'right',
    render(row) {
      return [
        withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'info',
              style: 'margin-right: 8px;',
              onClick: () => openOrderModal(row)
            },
            {
              default: () => '下单',
              icon: renderIcon('material-symbols:shopping-cart-outline', { size: 16 })
            }
          ),
          [[vPermission, 'post/api/v1/orders/create']]
        ),
        withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              style: 'margin-right: 8px;',
              onClick: () => handleEdit(row)
            },
            {
              default: () => '编辑',
              icon: renderIcon('material-symbols:edit-outline', { size: 16 })
            }
          ),
          [[vPermission, 'post/api/v1/food-menu/update']]
        ),
        h(
          NPopconfirm,
          {
            onPositiveClick: () => handleDelete({ id: row.id }, false),
            onNegativeClick: () => {},
          },
          {
            trigger: () =>
              withDirectives(
                h(
                  NButton,
                  {
                    size: 'small',
                    type: 'error',
                    style: 'margin-right: 8px;',
                  },
                  {
                    default: () => '删除',
                    icon: renderIcon('material-symbols:delete-outline', { size: 16 }),
                  }
                ),
                [[vPermission, 'delete/api/v1/food-menu/delete']]
              ),
            default: () => h('div', {}, '确定删除该菜单吗?'),
          }
        )
      ]
    }
  }
]



// 初始化数据
onMounted(() => {
  fetchDishOptions()
  $table.value?.handleSearch()
})
</script>

<template>
  <CommonPage show-footer title="食谱">
    <template #action>
      <NSpace>
        <NButton
          v-permission="'post/api/v1/food-menu/create'"
          type="primary"
          @click="handleAdd"
        >
          <template #icon>
            <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />
          </template>
          新建菜单
        </NButton>
      </NSpace>
    </template>

    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="api.getFoodMenuList"
    >
      <template #queryBar>
        <QueryBarItem label="菜单名称" :label-width="70">
          <NInput
            v-model:value="queryItems.food_menu_name"
            clearable
            type="text"
            placeholder="请输入菜单名称"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="日期" :label-width="70">
          <NDatePicker
            style="width: 150px;"
            v-model:value="queryItems.menu_date"
            type="date"
            clearable
            placeholder="请选择日期"
            @update:value="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="餐次" :label-width="70">
          <NSelect
            style="width: 150px;"
            v-model:value="queryItems.meal_type"
            clearable
            :options="mealTypeOptions"
            placeholder="请选择餐次"
            @update:value="$table?.handleSearch()"
          />
        </QueryBarItem>
      </template>
    </CrudTable>

    <CrudModal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :loading="modalLoading"
      @save="handleSave"
    >
      <NForm
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="modalForm"
        :disabled="modalAction === 'view'"
      >
        <NFormItem
          label="菜单名称"
          path="foodMenuName"
          :rule="{
            required: true,
            message: '请输入菜单名称',
            trigger: ['input', 'blur'],
          }"
        >
          <NInput v-model:value="modalForm.foodMenuName" placeholder="请输入菜单名称" />
        </NFormItem>

        <NFormItem
          label="日期"
          path="menuDate"
          :rule="{
            required: true,
            message: '请选择日期',
            trigger: ['change', 'blur'],
          }"
        >
          <NDatePicker
            v-model:value="modalForm.menuDate"
            type="date"
            placeholder="请选择日期"
          />
        </NFormItem>

        <NFormItem
          label="餐次"
          path="mealType"
          :rule="{
            required: true,
            message: '请选择餐次',
            trigger: ['change', 'blur'],
            type: 'number',
          }"
        >
          <NSelect
            v-model:value="modalForm.mealType"
            :options="mealTypeOptions"
            placeholder="请选择餐次"
          />
        </NFormItem>

        <NFormItem label="菜品列表">
          <div class="dish-list">
            <div v-for="(item, index) in modalForm.dishList" :key="index" class="dish-item">
              <div class="dish-row">
                <NSelect
                  v-model:value="item.dish_id"
                  filterable
                  :options="dishOptions"
                  placeholder="请选择菜品"
                  @update:value="(value) => updateDishInfo(index, value)"
                  class="dish-select"
                />
                <NButton
                  type="error"
                  circle
                  size="small"
                  @click="removeDish(index)"
                  class="remove-btn"
                >
                  <template #icon>
                    <TheIcon icon="material-symbols:delete-outline" :size="16" />
                  </template>
                </NButton>
              </div>
              <!-- 显示菜品详情 -->
              <div v-if="item.ingredients && item.ingredients.length > 0" class="dish-details">
                <h4>菜品食材：</h4>
                <div class="ingredients-list">
                  <NTag
                    v-for="ingredient in item.ingredients"
                    :key="ingredient.food_stuff_id"
                    type="info"
                    size="small"
                    style="margin: 2px;"
                  >
                    {{ ingredient.food_stuff_name }}: {{ ingredient.quantity }}{{ ingredient.unit_name }}
                  </NTag>
                </div>
              </div>
            </div>
          </div>
        </NFormItem>
        <NButton
          type="primary"
          @click="addDish"
          class="add-dish-btn"
        >
          <template #icon>
            <TheIcon icon="material-symbols:add" :size="16" />
          </template>
          添加菜品
        </NButton>
      </NForm>
    </CrudModal>

    <!-- 下单弹窗 -->
    <NModal
      v-model:show="showOrderModal"
      preset="card"
      title="生成订单"
      style="width: 800px"
      :mask-closable="true"
      :bordered="false"
      :loading="orderLoading"
    >
      <div v-if="currentMenu" style="padding: 16px 0">
        <NSpace vertical>
          <NCard title="菜单信息" size="small">
            <NSpace vertical>
              <div><strong>菜单名称：</strong>{{ currentMenu.food_menu_name }}</div>
              <div>
                <strong>日期：</strong>
                {{ currentMenu.menu_date ? formatDateForDisplay(currentMenu.menu_date) : '-' }}
              </div>
              <div>
                <strong>餐次：</strong>
                {{ mealTypeOptions.find(item => item.value === currentMenu.meal_type)?.label || currentMenu.meal_type }}
              </div>
            </NSpace>
          </NCard>

          <NCard title="学生信息" size="small">
            <NStatistic label="学生总数" :value="totalStudentCount" />
          </NCard>

          <NCard title="食材需求与库存" size="small" style="width: 100%">
            <NDataTable
              :columns="[
                { title: '食材名称', key: 'food_stuff_name', align: 'center' },
                { title: '单位数量', key: 'food_stuff_count', align: 'center' },
                { title: '单位', key: 'food_stuff_unit', align: 'center' },
                {
                  title: '需要数量',
                  key: 'required_amount',
                  align: 'center',
                  render: (row) => {
                    const amount = calculateRequiredAmount(row)
                    return h('span', `${amount} ${row.food_stuff_unit}`)
                  }
                },
                {
                  title: '当前库存',
                  key: 'store_amount',
                  align: 'center',
                  render: (row) => {
                    const amount = getStoreAmount(row.food_stuff_id)
                    return h('span', `${amount} ${row.food_stuff_unit}`)
                  }
                },
                {
                  title: '状态',
                  key: 'status',
                  align: 'center',
                  render: (row) => {
                    const required = calculateRequiredAmount(row)
                    const store = getStoreAmount(row.food_stuff_id)
                    const isEnough = store >= required

                    return h(
                      NTag,
                      { type: isEnough ? 'success' : 'error' },
                      { default: () => isEnough ? '库存充足' : '库存不足' }
                    )
                  }
                },
                {
                  title: '下单数量',
                  key: 'order_quantity',
                  align: 'center',
                  render: (row) => {
                    return h(NInputNumber, {
                      value: orderQuantities[row.food_stuff_id] || 0,
                      min: 0,
                      precision: 2,
                      step: 0.5,
                      style: 'width: 100px',
                      onUpdateValue: (value) => {
                        orderQuantities[row.food_stuff_id] = value
                      }
                    })
                  }
                }
              ]"
              :data="currentMenu.food_stuff_list"
              :bordered="false"
              size="small"
            />
          </NCard>
        </NSpace>
      </div>

      <template #footer>
        <div style="display: flex; justify-content: flex-end">
          <NSpace>
            <NButton @click="closeOrderModal">取消</NButton>
            <NPopconfirm
              positive-text="确认"
              negative-text="取消"
              @positive-click="submitOrder"
              placement="top"
            >
              <template #trigger>
                <NButton type="primary" :disabled="!currentMenu || !currentMenu.food_stuff_list || !currentMenu.food_stuff_list.length">确认下单</NButton>
              </template>
              确认要提交这个订单吗？
            </NPopconfirm>
          </NSpace>
        </div>
      </template>
    </NModal>
  </CommonPage>
</template>

<style scoped>
.dish-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.dish-item {
  margin-bottom: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  background-color: #fafafa;
}

.dish-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.dish-select {
  flex: 1;
}

.remove-btn {
  margin-left: 10px;
}

.dish-details {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #e0e0e0;
}

.dish-details h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #666;
}

.ingredients-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.add-dish-btn {
  margin-top: 10px;
}
</style>