<script setup>
import { h, onMounted, ref, resolveDirective, withDirectives, watch } from 'vue'
import { NAutoComplete, NButton, NForm, NFormItem, NInput, NInputNumber, NPopconfirm, NSelect } from 'naive-ui'
import { debounce } from 'lodash-es'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import TheIcon from '@/components/icon/TheIcon.vue'

import { renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import api from '@/api'

defineOptions({ name: '供应商管理' })

const $table = ref(null)
const queryItems = ref({})
const vPermission = resolveDirective('permission')

const {
  modalVisible,
  modalTitle,
  modalLoading,
  handleSave,
  modalForm,
  modalFormRef,
  handleEdit,
  handleDelete,
  handleAdd,
} = useCRUD({
  name: '供应商',
  initForm: {
    supplier_name: '',
  }, 
  doCreate: api.createDept,
  doUpdate: api.updateDept,
  doDelete: api.deleteDept,
  refresh: () => $table.value?.handleSearch(),
})

onMounted(() => {
  $table.value?.handleSearch()
})


const columns = [
  {
    title: '供应商名称',
    key: 'name',
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '电话',
    key: 'phone',
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '邮箱',
    key: 'email',
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '地址',
    key: 'address',
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '备注',
    key: 'desc',
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '操作',
    key: 'actions',
    width: 'auto',
    align: 'center',
    fixed: 'right',
    render(row) {
      return [
        withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              style: 'margin-left: 8px;',
              onClick: () => {
                handleEdit(row)
              },
            },
            {
              default: () => '编辑',
              icon: renderIcon('material-symbols:edit', { size: 16 }),
            }
          ),
          [[vPermission, 'post/api/v1/dept/update']]
        ),
        h(
          NPopconfirm,
          {
            onPositiveClick: () => handleDelete({ dept_id: row.id }, false),
            onNegativeClick: () => {},
          },
          {
            trigger: () =>
              withDirectives(
                h(
                  NButton,
                  {
                    size: 'small',
                    type: 'error',
                    style: 'margin-left: 8px;',
                  },
                  {
                    default: () => '删除',
                    icon: renderIcon('material-symbols:delete-outline', { size: 16 }),
                  }
                ),
                [[vPermission, 'delete/api/v1/dept/delete']]
              ),
            default: () => h('div', {}, '确定删除该供应商吗?'),
          }
        ),
        row.is_linked ? 
        h(
          NPopconfirm,
          {
            onPositiveClick: () => handleRemoveFromSchool(row.id),
            onNegativeClick: () => {},
          },
          {
            trigger: () =>
              withDirectives(
                h(
                  NButton,
                  {
                    size: 'small',
                    type: 'warning',
                    style: 'margin-left: 8px;',
                  },
                  {
                    default: () => '解除关联',
                    icon: renderIcon('material-symbols:link-off', { size: 16 }),
                  }
                ),
                [[vPermission, 'post/api/v1/dept/supplier/remove']]
              ),
            default: () => h('div', {}, '确定解除该供应商与学校的关联吗?'),
          }
        ) :
        h(
          NPopconfirm,
          {
            onPositiveClick: () => handleAddToSchool(row.id),
            onNegativeClick: () => {},
          },
          {
            trigger: () =>
              withDirectives(
                h(
                  NButton,
                  {
                    size: 'small',
                    type: 'success',
                    style: 'margin-left: 8px;',
                  },
                  {
                    default: () => '关联学校',
                    icon: renderIcon('material-symbols:link', { size: 16 }),
                  }
                ),
                [[vPermission, 'post/api/v1/dept/supplier/add']]
              ),
            default: () => h('div', {}, '确定将该供应商关联到学校吗?'),
          }
        )
      ]
    },
  },
]

const handleAddSupplier = async () => {
  try {
    const res = await api.addSupplier({ supplier_id: modalForm.value.supplier_id })
    if (res.code === 200) {
      $message.success('添加供应商成功')
      $table.value?.handleSearch()
      modalVisible.value = false
    } else {
      $message.error('添加供应商失败', res.message)
    }
  } catch (error) {
    $message.error('添加供应商失败', error)
  }
}
// 处理与学校的关联/解除关联
const handleAddToSchool = async (supplierID) => {
  try {
    await api.addSupplier({ supplier_id: supplierID })
    $table.value?.handleSearch()
    $message.success('添加供应商关联成功')
    modalVisible.value = false
  } catch (error) {
    $message.error('添加供应商关联失败', error)
  }
}

const handleRemoveFromSchool = async (supplierID) => {
  try {
    await api.removeSupplier({ supplier_id: supplierID })
    $table.value?.handleSearch()
    $message.success('移除供应商关联成功')
  } catch (error) {
    $message.error('移除供应商关联失败', error)
  }
}

const supplierLoading = ref(false)
const supplierOptions = ref([])

const fetchSupplierOptions = async (name) => {
  if (!name) {
    supplierOptions.value = []
    return
  }
  
  supplierLoading.value = true
  try {
    const res = await api.filterSuppliers({ name })
    supplierOptions.value = res.data
  } catch (error) {
    $message.error('获取供应商列表失败', error)
    supplierOptions.value = []
  } finally {
    supplierLoading.value = false
  }
}

const debouncedFetchOptions = debounce(fetchSupplierOptions, 300)

watch(() => modalForm.value.supplier_name, (newValue) => {
  debouncedFetchOptions(newValue)
}, { immediate: true })
</script>

<template>
  <!-- 业务页面 -->
  <CommonPage show-footer title="供应商列表">
    <template #action>
      <div>
        <NButton v-permission="'post/api/v1/dept/create'" class="float-right mr-15" type="primary" @click="handleAdd">
          <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />添加供应商
        </NButton>
      </div>
    </template>
    <!-- 表格 -->
    <CrudTable ref="$table" v-model:query-items="queryItems" :columns="columns" :get-data="api.getSuppliers">
      <template #queryBar>
        <QueryBarItem label="供应商名称" :label-width="80">
          <NInput v-model:value="queryItems.name" clearable type="text" placeholder="请输入供应商名称"
            @keypress.enter="$table?.handleSearch()" />
        </QueryBarItem>
        <QueryBarItem label="电话" :label-width="80">
          <NInput v-model:value="queryItems.phone" clearable type="text" placeholder="请输入电话号码"
            @keypress.enter="$table?.handleSearch()" />
        </QueryBarItem>
      </template>
    </CrudTable>

    <!-- 新增/编辑 弹窗 -->
    <CrudModal v-model:visible="modalVisible" :title="modalTitle" :loading="modalLoading" @save="handleAddSupplier">
      <NForm ref="modalFormRef" label-placement="left" label-align="left" :label-width="80" :model="modalForm"
        >
        <NFormItem label="供应商" path="name">
          <NAutoComplete :loading="supplierLoading" :on-select="(value) => modalForm.supplier_id = value" v-model:value="modalForm.supplier_name" :options="supplierOptions" clearable placeholder="请输入供应商名称" />
        </NFormItem>
      </NForm>
    </CrudModal>
  </CommonPage>
</template> 