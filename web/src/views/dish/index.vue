<script setup>
import { h, onMounted, ref, resolveDirective, withDirectives, watch } from 'vue'
import {
  NButton,
  NForm,
  NFormItem,
  NInput,
  NPopconfirm,
  NSelect,
  NSpace,
  NTag,
  NInputNumber,
  useMessage
} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import TheIcon from '@/components/icon/TheIcon.vue'

import { formatDate, renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import api from '@/api'

defineOptions({ name: '菜品管理' })

const $message = useMessage()
const $table = ref(null)
const queryItems = ref({})
const vPermission = resolveDirective('permission')

// 食材选项
const foodStuffOptions = ref([])
const unitOptions = ref([])

// 获取食材列表
async function fetchFoodStuffOptions() {
  try {
    const res = await api.getFoodStuffList({ size: 9999 })
    if (res && res.data) {
      foodStuffOptions.value = res.data.map(item => ({
        label: item.food_stuff_name,
        value: item.id,
        unitId: item.food_stuff_unit_id,
        unitName: item.unit_name
      }))
    }
  } catch (error) {
    console.error('加载食材数据失败:', error)
  }
}

// 获取单位列表
async function fetchUnitOptions() {
  try {
    const res = await api.getUnitList({ size: 9999 })
    if (res && res.data) {
      unitOptions.value = res.data.map(item => ({
        label: item.unit_name,
        value: item.id
      }))
    }
  } catch (error) {
    console.error('加载单位数据失败:', error)
  }
}

// 添加食材到菜品
function addIngredient() {
  modalForm.value.ingredients.push({
    foodStuffId: null,
    foodStuffName: '',
    unitName: '',
    quantity: 0
  })
}

// 移除食材
function removeIngredient(index) {
  if (modalForm.value.ingredients && modalForm.value.ingredients.length > index) {
    modalForm.value.ingredients.splice(index, 1)
  }
}

// 当选择食材时，自动填充食材名称和单位
function updateIngredientInfo(index, foodStuffId) {
  if (!modalForm.value.ingredients) return

  const selectedFoodStuff = foodStuffOptions.value.find(item => item.value === foodStuffId)
  if (selectedFoodStuff) {
    const newItem = {
      foodStuffId: foodStuffId,
      foodStuffName: selectedFoodStuff.label,
      unitName: selectedFoodStuff.unitName,
      quantity: 0
    }
    modalForm.value.ingredients.splice(index, 1, newItem)
  }
}

const {
  modalVisible,
  modalAction,
  modalTitle,
  modalLoading,
  handleAdd,
  handleDelete,
  handleEdit: originalHandleEdit,
  handleSave,
  modalForm,
  modalFormRef,
} = useCRUD({
  name: '菜品',
  initForm: {
    dishName: '',
    ingredients: [],
    isActive: 1
  },
  doCreate: api.createDish,
  doDelete: api.deleteDish,
  doUpdate: api.updateDish,
  refresh: () => $table.value?.handleSearch(),
})

// 监听弹窗关闭，清空表单内容
watch(modalVisible, (newVal, oldVal) => {
  if (oldVal && !newVal) { // 弹窗从打开变为关闭
    // 延迟清空表单，确保弹窗完全关闭后再清空

    setTimeout(() => {
      // modalForm.value = {
      //   dishName: '',
      //   ingredients: [],
      //   isActive: true
      // }
      modalForm.value.dishName = ''
      modalForm.value.ingredients = []
      modalForm.value.isActive  = 1
    }, 100)
  }
})

// 重写handleAdd和handleEdit方法，在打开弹窗前刷新选项数据
// const handleAdd = async () => {
//   await fetchFoodStuffOptions()
//   await fetchUnitOptions()
//   // 确保表单完全重置
//   modalForm.value = {
//     dishName: '',
//     ingredients: [],
//     isActive: true
//   }
//   addIngredient() // 默认添加一个食材项
//   originalHandleAdd()
// }

const handleEdit = async (row) => {
  await fetchFoodStuffOptions()
  await fetchUnitOptions()
  originalHandleEdit(row)
}

const columns = [
  {
    title: '菜品名称',
    key: 'dish_name',
    width: 150,
    align: 'center',
    ellipsis: { tooltip: true }
  },
  {
    title: '食材数量',
    key: 'ingredients',
    width: 100,
    align: 'center',
    render(row) {
      return h('span', `${row.ingredients?.length || 0}种`)
    }
  },
  {
    title: '状态',
    key: 'is_active',
    width: 80,
    align: 'center',
    render(row) {
      return h(
        NTag,
        { type: row.is_active ? 'success' : 'error' },
        { default: () => row.is_active ? '启用' : '禁用' }
      )
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 150,
    align: 'center',
    render(row) {
      return h('span', formatDate(row.created_at))
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'center',
    fixed: 'right',
    render(row) {
      return [
        withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'info',
              style: 'margin-right: 8px;',
              onClick: () => {
                handleEdit(row)
                modalAction.value = 'view'
              },
            },
            {
              default: () => '查看',
              icon: renderIcon('material-symbols:visibility-outline', { size: 16 }),
            }
          ),
          [[vPermission, 'get/api/v1/dish/get']]
        ),
        withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              style: 'margin-right: 8px;',
              onClick: () => {
                handleEdit(row)
              },
            },
            {
              default: () => '编辑',
              icon: renderIcon('material-symbols:edit-outline', { size: 16 }),
            }
          ),
          [[vPermission, 'post/api/v1/dish/update']]
        ),
        h(
          NPopconfirm,
          {
            onPositiveClick: () => handleDelete({ id: row.id }, false),
            onNegativeClick: () => {},
          },
          {
            trigger: () =>
              withDirectives(
                h(
                  NButton,
                  {
                    size: 'small',
                    type: 'error',
                  },
                  {
                    default: () => '删除',
                    icon: renderIcon('material-symbols:delete-outline', { size: 16 }),
                  }
                ),
                [[vPermission, 'delete/api/v1/dish/delete']]
              ),
            default: () => h('div', {}, '确定删除该菜品吗?'),
          }
        )
      ]
    }
  }
]

// 初始化数据
onMounted(() => {
  fetchFoodStuffOptions()
  fetchUnitOptions()
  $table.value?.handleSearch()
})
</script>

<template>
  <CommonPage show-footer title="菜品管理">
    <template #action>
      <NSpace>
        <NButton v-permission="'post/api/v1/dish/create'" type="primary" @click="handleAdd">
          <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />新增菜品
        </NButton>
      </NSpace>
    </template>

    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="api.getDishList"
    >
      <template #queryBar>
        <QueryBarItem label="菜品名称" :label-width="70">
          <NInput
            v-model:value="queryItems.dishName"
            clearable
            type="text"
            placeholder="请输入菜品名称"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="状态" :label-width="70">
          <NSelect
            style="width: 150px;"
            v-model:value="queryItems.isActive"
            clearable
            :options="[
              { label: '启用', value: 1 },
              { label: '禁用', value: 0 }
            ]"
            placeholder="请选择状态"
            @update:value="$table?.handleSearch()"
          />
        </QueryBarItem>
      </template>
    </CrudTable>

    <CrudModal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :loading="modalLoading"
      @save="handleSave"
    >
      <NForm
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="modalForm"
        :disabled="modalAction === 'view'"
      >
        <NFormItem
          label="菜品名称"
          path="dishName"
          :rule="{
            required: true,
            message: '请输入菜品名称',
            trigger: ['input', 'blur'],
          }"
        >
          <NInput v-model:value="modalForm.dishName" placeholder="请输入菜品名称" />
        </NFormItem>

        <NFormItem
          label="状态"
          path="isActive"
        >
          <NSelect
            v-model:value="modalForm.isActive"
            :options="[
              { label: '启用', value: 1 },
              { label: '禁用', value: 0 }
            ]"
            placeholder="请选择状态"
          />
        </NFormItem>

        <NFormItem label="食材列表">
          <div class="ingredient-list">
            <div v-for="(item, index) in modalForm.ingredients" :key="index" class="ingredient-item">
              <div class="ingredient-row">
                <NSelect
                  v-model:value="item.foodStuffId"
                  filterable
                  :options="foodStuffOptions"
                  placeholder="请选择食材"
                  @update:value="(value) => updateIngredientInfo(index, value)"
                  class="ingredient-select"
                  :disabled="modalAction === 'view'"
                />
                <NInputNumber
                  v-model:value="item.quantity"
                  placeholder="数量"
                  :min="0"
                  :step="0.1"
                  :precision="2"
                  class="ingredient-count"
                  :disabled="modalAction === 'view'"
                />
                <span class="ingredient-unit">{{ item.unitName || '单位' }}</span>
                <NButton
                  v-if="modalAction !== 'view'"
                  type="error"
                  circle
                  size="small"
                  @click="removeIngredient(index)"
                  class="remove-btn"
                >
                  <template #icon>
                    <TheIcon icon="material-symbols:delete-outline" :size="16" />
                  </template>
                </NButton>
              </div>
            </div>
          </div>
        </NFormItem>
        <NButton
          v-if="modalAction !== 'view'"
          type="primary"
          @click="addIngredient"
          class="add-ingredient-btn"
        >
          <template #icon>
            <TheIcon icon="material-symbols:add" :size="16" />
          </template>
          添加食材
        </NButton>
      </NForm>
    </CrudModal>
  </CommonPage>
</template>

<style scoped>
.ingredient-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.ingredient-item {
  margin-bottom: 10px;
}

.ingredient-row {
  display: flex;
  align-items: center;
}

.ingredient-select {
  flex: 1;
}

.ingredient-count {
  width: 120px;
  margin: 0 10px;
}

.ingredient-unit {
  width: 60px;
  text-align: center;
}

.remove-btn {
  margin-left: 10px;
}

.add-ingredient-btn {
  margin-top: 10px;
}
</style>